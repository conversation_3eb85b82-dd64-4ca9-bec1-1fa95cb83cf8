import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { insertContactRequestSchema, type InsertContactRequest } from "@shared/schema";
import { motion, useInView } from "framer-motion";
import { useRef } from "react";
import {
  Menu,
  X,
  Phone,
  Mail,
  MapPin,
  Waves,
  Wrench,
  Square,
  Layers,
  Settings,
  Lightbulb,
  Facebook,
  Instagram,
  Linkedin,
  Globe,
  Camera
} from "lucide-react";
import pool1 from "../assets/pool1_1749845961926.jpg";
import pool2 from "../assets/pool2_1749845961926.jpg";
import pool3 from "../assets/pool3_1749845961925.jpg";
import pool4 from "../assets/pool4_1749845961925.jpg";
import logo from "../assets/logo.jpg";

export default function Home() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const { toast } = useToast();

  // Set loaded state after component mounts
  useEffect(() => {
    setIsLoaded(true);
  }, []);

  // Animation variants
  const fadeInUp = {
    hidden: { opacity: 0, y: 60 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const slideInLeft = {
    hidden: { opacity: 0, x: -60 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.8, ease: "easeOut" }
    }
  };

  const slideInRight = {
    hidden: { opacity: 0, x: 60 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.8, ease: "easeOut" }
    }
  };

  const scaleIn = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.5, ease: "easeOut" }
    }
  };

  const form = useForm<InsertContactRequest>({
    resolver: zodResolver(insertContactRequestSchema),
    defaultValues: {
      name: "",
      phone: "",
      email: "",
      address: "",
      message: "",
    },
  });

  const contactMutation = useMutation({
    mutationFn: async (data: InsertContactRequest) => {
      const response = await apiRequest("POST", "/api/contact", data);
      return response.json();
    },
    onSuccess: (data) => {
      toast({
        title: "Success!",
        description: data.message,
      });
      form.reset();
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to send message. Please try again.",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: InsertContactRequest) => {
    contactMutation.mutate(data);
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
    setIsMenuOpen(false);
  };

  const services = [
    {
      icon: <Wrench className="w-8 h-8" />,
      title: "Plaster",
      description: "Professional pool plastering with durable finishes that restore your pool's surface to pristine condition."
    },
    {
      icon: <Square className="w-8 h-8" />,
      title: "Tile",
      description: "Expert tile installation and repair using premium materials for waterline, spa, and decorative applications."
    },
    {
      icon: <Layers className="w-8 h-8" />,
      title: "Coping",
      description: "Professional coping installation and renovation for pool edges with natural stone and concrete options."
    },
    {
      icon: <Layers className="w-8 h-8" />,
      title: "Decking",
      description: "Complete deck renovation and resurfacing with slip-resistant materials and modern aesthetic appeal."
    },
    {
      icon: <Settings className="w-8 h-8" />,
      title: "Pumps",
      description: "High-efficiency pump installation and maintenance for optimal water circulation and energy savings."
    },
    {
      icon: <Lightbulb className="w-8 h-8" />,
      title: "LEDs",
      description: "Advanced LED lighting systems with color-changing options for enhanced ambiance and safety."
    }
  ];

  // Project photos
  const projectGallery = [
    {
      src: pool1,
      alt: "Modern infinity pool with spa and stunning hill country views",
      title: "Infinity Pool & Spa",
      location: "Austin, TX"
    },
    {
      src: pool2, 
      alt: "Elegant geometric pool with pristine blue water and natural stone coping",
      title: "Geometric Pool Design",
      location: "San Antonio, TX"
    },
    {
      src: pool3,
      alt: "Luxury spa with decorative tile work and modern water features",
      title: "Custom Spa Installation",
      location: "Houston, TX"
    },
    {
      src: pool4,
      alt: "Commercial pool renovation with new plaster and tile finish",
      title: "Pool Renovation",
      location: "Austin, TX"
    }
  ];

  return (
    <motion.div
      className="min-h-screen"
      initial={{ opacity: 0 }}
      animate={{ opacity: isLoaded ? 1 : 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Navigation */}
      <motion.nav
        className="fixed top-0 w-full bg-white/95 backdrop-blur-sm z-50 shadow-sm"
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <img src={logo} alt="Platinum Pool and Spa" className="w-12 h-12 object-contain" />
              <div>
                <div className="font-bold text-[var(--brand-blue)] text-lg">Platinum Pool & Spa</div>
                <div className="text-xs text-gray-500">ChillAtMyPool.com</div>
              </div>
            </div>
            
            {/* Desktop Navigation */}
            <div className="hidden md:flex space-x-8">
              <button onClick={() => scrollToSection('services')} className="text-gray-700 hover:text-[var(--brand-blue)] transition-colors">
                Services
              </button>
              <button onClick={() => scrollToSection('gallery')} className="text-gray-700 hover:text-[var(--brand-blue)] transition-colors">
                Gallery
              </button>
              <button onClick={() => scrollToSection('contact')} className="text-gray-700 hover:text-[var(--brand-blue)] transition-colors">
                Contact
              </button>
            </div>
            
            <div className="flex items-center space-x-4">
              <Button onClick={() => scrollToSection('contact')} className="bg-[var(--brand-aqua)] text-white hover:bg-[var(--brand-light-blue)] transition-colors font-medium">
                Request Service
              </Button>
              
              {/* Mobile Menu Button */}
              <button 
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="md:hidden"
              >
                {isMenuOpen ? <X /> : <Menu />}
              </button>
            </div>
          </div>
          
          {/* Mobile Menu */}
          {isMenuOpen && (
            <div className="md:hidden border-t border-gray-200 py-4">
              <div className="flex flex-col space-y-2">
                <button onClick={() => scrollToSection('services')} className="text-left text-gray-700 hover:text-[var(--brand-blue)] py-2">
                  Services
                </button>
                <button onClick={() => scrollToSection('gallery')} className="text-left text-gray-700 hover:text-[var(--brand-blue)] py-2">
                  Gallery
                </button>
                <button onClick={() => scrollToSection('contact')} className="text-left text-gray-700 hover:text-[var(--brand-blue)] py-2">
                  Contact
                </button>
              </div>
            </div>
          )}
        </div>
      </motion.nav>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center pt-20 overflow-hidden">
        <motion.div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `url('${pool2}')`
          }}
          initial={{ scale: 1.1 }}
          animate={{ scale: 1 }}
          transition={{ duration: 1.5, ease: "easeOut" }}
        />
        <div className="absolute inset-0 bg-gradient-to-r from-[var(--brand-blue)]/80 via-[var(--brand-blue)]/60 to-transparent" />

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-white">
          <motion.div
            className="max-w-5xl py-8 md:py-16"
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
          >
            <motion.h1
              className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6 max-w-4xl"
              variants={slideInLeft}
            >
              Commercial Pool Renovation{" "}
              <span className="block">You Can Count On</span>
            </motion.h1>
            <motion.p
              className="text-lg md:text-xl lg:text-2xl mb-8 text-blue-100 max-w-3xl leading-relaxed"
              variants={slideInLeft}
            >
              Serving Austin, Houston & San Antonio with expert tile, coping, decking, and lighting upgrades.
            </motion.p>
            <motion.div
              className="flex flex-col sm:flex-row gap-4 mt-2"
              variants={fadeInUp}
            >
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  onClick={() => scrollToSection('contact')}
                  className="bg-[var(--brand-aqua)] text-white px-10 py-4 text-lg font-semibold hover:bg-[var(--brand-light-blue)] transition-all shadow-lg w-full sm:w-auto rounded-lg"
                >
                  Request Service
                </Button>
              </motion.div>
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  onClick={() => scrollToSection('gallery')}
                  variant="outline"
                  className="border-2 border-white bg-white/10 text-white px-10 py-4 text-lg font-semibold hover:bg-white hover:text-[var(--brand-blue)] transition-all w-full sm:w-auto rounded-lg"
                >
                  View Our Work
                </Button>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            variants={fadeInUp}
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Our Specialties</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We provide comprehensive commercial pool renovation services with expert craftsmanship and premium materials.
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.2 }}
            variants={staggerContainer}
          >
            {services.map((service, index) => (
              <motion.div key={index} variants={scaleIn}>
                <Card className="bg-white shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1 h-full">
                  <CardContent className="p-8">
                    <motion.div
                      className="w-16 h-16 bg-gradient-to-br from-[var(--brand-blue)] to-[var(--brand-aqua)] rounded-xl flex items-center justify-center mb-6 text-white animate-float"
                      whileHover={{
                        scale: 1.1,
                        rotate: 5,
                        boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
                      }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      {service.icon}
                    </motion.div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">{service.title}</h3>
                    <p className="text-gray-600 leading-relaxed">
                      {service.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Gallery Section */}
      <section id="gallery" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            variants={fadeInUp}
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Recent Projects</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Explore our latest commercial pool renovation projects across Austin, Houston, and San Antonio.
            </p>
          </motion.div>

          {/* Project Gallery */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.1 }}
            variants={staggerContainer}
          >
            {projectGallery.map((project, index) => (
              <motion.div
                key={index}
                className="group relative overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-all"
                variants={scaleIn}
                whileHover={{ y: -8 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <motion.img
                  src={project.src}
                  alt={project.alt}
                  className="w-full h-64 object-cover"
                  whileHover={{ scale: 1.1 }}
                  transition={{ duration: 0.3 }}
                />
                <motion.div
                  className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"
                  initial={{ opacity: 0 }}
                  whileHover={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  <motion.div
                    className="absolute bottom-4 left-4 text-white"
                    initial={{ y: 20, opacity: 0 }}
                    whileHover={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.3, delay: 0.1 }}
                  >
                    <p className="font-semibold">{project.title}</p>
                    <p className="text-sm text-gray-200">{project.location}</p>
                  </motion.div>
                </motion.div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section id="contact" className="py-20 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            variants={fadeInUp}
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Need a Quote?</h2>
            <p className="text-xl text-gray-600">
              Get in touch with us for a free consultation and estimate for your commercial pool renovation project.
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.2 }}
            variants={scaleIn}
          >
            <Card className="shadow-xl">
              <CardContent className="p-8 md:p-12">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-semibold text-gray-700">Name *</FormLabel>
                          <FormControl>
                            <Input 
                              {...field} 
                              placeholder="Your full name"
                              className="px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-[var(--brand-aqua)] focus:border-transparent transition-all"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-semibold text-gray-700">Phone *</FormLabel>
                          <FormControl>
                            <Input 
                              {...field} 
                              placeholder="(*************"
                              className="px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-[var(--brand-aqua)] focus:border-transparent transition-all"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-semibold text-gray-700">Email *</FormLabel>
                          <FormControl>
                            <Input 
                              {...field} 
                              type="email"
                              placeholder="<EMAIL>"
                              className="px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-[var(--brand-aqua)] focus:border-transparent transition-all"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="address"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-semibold text-gray-700">Address</FormLabel>
                          <FormControl>
                            <Input 
                              {...field} 
                              placeholder="Project location (optional)"
                              className="px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-[var(--brand-aqua)] focus:border-transparent transition-all"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="message"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-semibold text-gray-700">Project Details</FormLabel>
                        <FormControl>
                          <Textarea 
                            {...field} 
                            placeholder="Tell us about your pool renovation needs..."
                            rows={5}
                            className="px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-[var(--brand-aqua)] focus:border-transparent transition-all resize-none"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="text-center">
                    <Button 
                      type="submit" 
                      disabled={contactMutation.isPending}
                      className="bg-[var(--brand-aqua)] text-white px-12 py-4 text-lg font-semibold hover:bg-[var(--brand-light-blue)] transition-all transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {contactMutation.isPending ? "Sending..." : "Request Service"}
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-[var(--brand-blue)] text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.2 }}
            variants={staggerContainer}
          >
            {/* Logo and Company Info */}
            <motion.div className="space-y-4" variants={fadeInUp}>
              <div className="flex items-center space-x-3">
                <img src={logo} alt="Platinum Pool and Spa" className="w-12 h-12 object-contain bg-white rounded-lg p-1" />
                <div>
                  <div className="font-bold text-xl">Platinum Pool & Spa</div>
                  <div className="text-blue-200 text-sm">ChillAtMyPool.com</div>
                </div>
              </div>
              <p className="text-blue-200 leading-relaxed">
                Professional commercial pool renovation services across Texas. Expert craftsmanship, premium materials, exceptional results.
              </p>
            </motion.div>

            {/* Contact Info */}
            <motion.div className="space-y-4" variants={fadeInUp}>
              <h3 className="text-xl font-bold">Contact</h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Mail className="w-5 h-5 text-[var(--brand-aqua)]" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center space-x-2">
                  <MapPin className="w-5 h-5 text-[var(--brand-aqua)]" />
                  <span>Austin • Houston • San Antonio</span>
                </div>
              </div>
            </motion.div>

            {/* Social Links */}
            <motion.div className="space-y-4" variants={fadeInUp}>
              <h3 className="text-xl font-bold">Follow Us</h3>
              <div className="flex space-x-4">
                <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    variant="outline"
                    size="icon"
                    className="border-2 border-white bg-white/10 text-white hover:bg-white hover:text-[var(--brand-blue)] transition-all"
                    onClick={() => window.open('https://www.instagram.com/platinumpoolandspallc/', '_blank')}
                  >
                    <Instagram className="w-5 h-5" />
                  </Button>
                </motion.div>
                <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    variant="outline"
                    size="icon"
                    className="border-2 border-white bg-white/10 text-white hover:bg-white hover:text-[var(--brand-blue)] transition-all"
                  >
                    <Facebook className="w-5 h-5" />
                  </Button>
                </motion.div>
                <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    variant="outline"
                    size="icon"
                    className="border-2 border-white bg-white/10 text-white hover:bg-white hover:text-[var(--brand-blue)] transition-all"
                  >
                    <Linkedin className="w-5 h-5" />
                  </Button>
                </motion.div>
              </div>
            </motion.div>
          </motion.div>

          <div className="border-t border-blue-600 mt-12 pt-8 text-center text-blue-200">
            <p>&copy; 2025 Platinum Pool and Spa. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </motion.div>
  );
}